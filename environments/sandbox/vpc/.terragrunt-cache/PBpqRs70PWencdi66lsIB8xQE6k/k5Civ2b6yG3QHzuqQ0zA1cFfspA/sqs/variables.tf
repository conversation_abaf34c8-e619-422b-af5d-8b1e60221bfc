variable "environment" {
  description = "Environment name (dev, sandbox, prod, etc.)"
  type        = string
}

variable "tags" {
  description = "Tags to apply to all resources"
  type        = map(string)
  default = {
    "Terraform" = "true"
    "Owner"     = "DevOps"
  }
}

variable "enable_indexing_queue" {
  description = "Enable indexing queue"
  type        = bool
  default     = true
}

variable "enable_usage_queue" {
  description = "Enable usage tracking queue"
  type        = bool
  default     = true
}

variable "enable_notifications_queue" {
  description = "Enable notifications queue"
  type        = bool
  default     = true
}

variable "visibility_timeout_seconds" {
  description = "Visibility timeout for SQS messages"
  type        = number
  default     = 300
}

variable "message_retention_seconds" {
  description = "Message retention period in seconds"
  type        = number
  default     = 1209600  # 14 days
}

variable "max_receive_count" {
  description = "Maximum number of receives before sending to DLQ"
  type        = number
  default     = 3
}
