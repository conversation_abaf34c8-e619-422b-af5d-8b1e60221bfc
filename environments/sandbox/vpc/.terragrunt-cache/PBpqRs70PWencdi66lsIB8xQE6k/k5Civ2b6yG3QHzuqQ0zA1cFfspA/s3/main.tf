terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

# S3 Bucket for AI Core Models
resource "aws_s3_bucket" "ai_core_models" {
  bucket = "${var.environment}-ai-core-models"

  tags = merge(var.tags, {
    Name = "${var.environment}-ai-core-models"
    Service = "ai-core"
  })
}

resource "aws_s3_bucket_versioning" "ai_core_models" {
  bucket = aws_s3_bucket.ai_core_models.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "ai_core_models" {
  bucket = aws_s3_bucket.ai_core_models.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

# S3 Bucket for File Repository
resource "aws_s3_bucket" "file_repository" {
  bucket = "${var.environment}-file-repository"

  tags = merge(var.tags, {
    Name = "${var.environment}-file-repository"
    Service = "ai-core-indexer"
  })
}

resource "aws_s3_bucket_versioning" "file_repository" {
  bucket = aws_s3_bucket.file_repository.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "file_repository" {
  bucket = aws_s3_bucket.file_repository.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

# S3 Bucket for Backups
resource "aws_s3_bucket" "backups" {
  bucket = "${var.environment}-backups"

  tags = merge(var.tags, {
    Name = "${var.environment}-backups"
    Service = "backup"
  })
}

resource "aws_s3_bucket_versioning" "backups" {
  bucket = aws_s3_bucket.backups.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "backups" {
  bucket = aws_s3_bucket.backups.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

# S3 Bucket for Logs
resource "aws_s3_bucket" "logs" {
  bucket = "${var.environment}-logs"

  tags = merge(var.tags, {
    Name = "${var.environment}-logs"
    Service = "logging"
  })
}

resource "aws_s3_bucket_versioning" "logs" {
  bucket = aws_s3_bucket.logs.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "logs" {
  bucket = aws_s3_bucket.logs.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}
