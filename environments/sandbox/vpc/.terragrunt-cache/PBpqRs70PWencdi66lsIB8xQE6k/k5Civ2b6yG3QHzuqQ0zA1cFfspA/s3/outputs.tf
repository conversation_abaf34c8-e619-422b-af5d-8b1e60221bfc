output "ai_core_models_bucket_name" {
  description = "Name of the AI core models S3 bucket"
  value       = aws_s3_bucket.ai_core_models.bucket
}

output "ai_core_models_bucket_arn" {
  description = "ARN of the AI core models S3 bucket"
  value       = aws_s3_bucket.ai_core_models.arn
}

output "file_repository_bucket_name" {
  description = "Name of the file repository S3 bucket"
  value       = aws_s3_bucket.file_repository.bucket
}

output "file_repository_bucket_arn" {
  description = "ARN of the file repository S3 bucket"
  value       = aws_s3_bucket.file_repository.arn
}

output "backups_bucket_name" {
  description = "Name of the backups S3 bucket"
  value       = aws_s3_bucket.backups.bucket
}

output "backups_bucket_arn" {
  description = "ARN of the backups S3 bucket"
  value       = aws_s3_bucket.backups.arn
}

output "logs_bucket_name" {
  description = "Name of the logs S3 bucket"
  value       = aws_s3_bucket.logs.bucket
}

output "logs_bucket_arn" {
  description = "ARN of the logs S3 bucket"
  value       = aws_s3_bucket.logs.arn
}

output "all_bucket_arns" {
  description = "List of all S3 bucket ARNs"
  value = [
    aws_s3_bucket.ai_core_models.arn,
    aws_s3_bucket.file_repository.arn,
    aws_s3_bucket.backups.arn,
    aws_s3_bucket.logs.arn
  ]
}
