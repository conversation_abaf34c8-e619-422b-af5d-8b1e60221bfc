# n5-terraform-aws-iam

<!-- BEGINNING OF PRE-COMMIT-TERRAFORM DOCS HOOK -->

## Scope

The aim of this Terraform module is to create and manage AWS resources for user, role, and policy management. These resources are fundamental for managing access and permissions in AWS environments.

## Usage

The module will provide the following resources:

IAM User: This resource represents an IAM user in AWS. IAM users are identities with unique access credentials used to access AWS services and resources securely.

IAM Role: This resource represents an IAM role in AWS. IAM roles are entities that define a set of permissions and are used to delegate access to users, applications, or services.

IAM Policy: This resource represents an IAM policy in AWS. IAM policies are JSON documents that define the permissions and actions allowed or denied on AWS resources.
### Clone project

```hcl
git clone  <repository_url>
cd <project_folder_name>/
## To do plan/apply your code create an PR on Azure DevOps.
```
### Installation of pre-commit for Ubuntu 20.04 and higher

```hcl
sudo apt update
sudo apt install -y unzip software-properties-common python3 python3-pip
python3 -m pip install --upgrade pip
pip/pip3 install -r requirements.txt
curl -L "$(curl -s https://api.github.com/repos/terraform-docs/terraform-docs/releases/latest | grep -o -E -m 1 "https://.+?-linux-amd64.tar.gz")" > terraform-docs.tgz && tar -xzf terraform-docs.tgz terraform-docs && rm terraform-docs.tgz && chmod +x terraform-docs && sudo mv terraform-docs /usr/bin/
cd <project_folder_name>/
pre-commit install
```

### Pipeline
From the pipeline you receive the variables:
- creator
- repository
- role_arn
A pipeline is included in this repository in the azure-pipelines.yml file.

Once an PR on main is approved, 3 stages are triggered:
#### terraform-link:
- terraform_lint
- terraform fmt -check
- terraform validate
#### publish_tag:
- create the tag using versioning configured in the package.json file


### Versioned

Once the project has been created and before working on a new improvement or fix, the following keys must be configured in the package.json file:
- name (project name)
- description (project description)
- type repository (git)
- repository url (repository url)
- version (place the version that will be used for the tag)

See [Conventional Commits](https://www.conventionalcommits.org/en/v1.0.0/)

## Providers

No providers.

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_iam_access_key_id"></a> [iam\_access\_key\_id](#output\_iam\_access\_key\_id) | The access key ID |
| <a name="output_iam_access_key_secret"></a> [iam\_access\_key\_secret](#output\_iam\_access\_key\_secret) | The access key secret |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_create_iam_access_key"></a> [create\_iam\_access\_key](#input\_create\_iam\_access\_key) | The `create_iam_access_key` variable determines whether to create an IAM access key for the user | `bool` | `true` | no |
| <a name="input_create_policy"></a> [create\_policy](#input\_create\_policy) | n/a | `bool` | `false` | no |
| <a name="input_create_role"></a> [create\_role](#input\_create\_role) | The `create_role` variable determines whether to create a new IAM role | `bool` | `true` | no |
| <a name="input_create_role_oidc"></a> [create\_role\_oidc](#input\_create\_role\_oidc) | The `create_role_oidc` variable determines whether to create an IAM role with an OpenID Connect (OIDC) provider | `bool` | `false` | no |
| <a name="input_create_user"></a> [create\_user](#input\_create\_user) | n/a | `bool` | `false` | no |
| <a name="input_force_destroy"></a> [force\_destroy](#input\_force\_destroy) | The `force_destroy` variable specifies whether to force the destruction of the resource, even if it contains data | `bool` | `true` | no |
| <a name="input_password_reset_required"></a> [password\_reset\_required](#input\_password\_reset\_required) | The `password_reset_required` variable specifies whether the IAM user is required to reset their password at the next sign-in | `bool` | `false` | no |
| <a name="input_path"></a> [path](#input\_path) | The `path` variable specifies the path for a resource in AWS Identity and Access Management (IAM) | `string` | `"/"` | no |
| <a name="input_policies"></a> [policies](#input\_policies) | The `policies` variable is used to define a list of IAM policies to be attached to a resource | `any` | <pre>{<br>  "Statement": [<br>    {<br>      "Action": "sts:AssumeRole",<br>      "Effect": "Allow",<br>      "Resource": [<br>        "arn:aws:iam:::role/"<br>      ],<br>      "Sid": "VisualEditor0"<br>    }<br>  ],<br>  "Version": "2012-10-17"<br>}</pre> | no |
| <a name="input_policy_description"></a> [policy\_description](#input\_policy\_description) | The `policy_description` variable provides a description for the IAM policy | `string` | `"policy_description"` | no |
| <a name="input_policy_name"></a> [policy\_name](#input\_policy\_name) | The `policy_name` variable specifies the name of the policy | `string` | `"policy"` | no |
| <a name="input_provider_url_oidc"></a> [provider\_url\_oidc](#input\_provider\_url\_oidc) | The `provider_url_oidc` variable specifies the URL of the OpenID Connect (OIDC) provider | `string` | `"oidc.eks.eu-west-1.amazonaws.com/id/000000000000000000000000"` | no |
| <a name="input_region"></a> [region](#input\_region) | AWS region | `string` | `"us-east-1"` | no |
| <a name="input_role_name"></a> [role\_name](#input\_role\_name) | The `role_name` variable specifies the name of the IAM role | `string` | `"role"` | no |
| <a name="input_role_name_oidc"></a> [role\_name\_oidc](#input\_role\_name\_oidc) | The `role_name_oidc` variable specifies the name of the IAM role associated with the OpenID Connect (OIDC) provider | `string` | `"role_oidc"` | no |
| <a name="input_role_requires_mfa"></a> [role\_requires\_mfa](#input\_role\_requires\_mfa) | The `role_requires_mfa` variable determines whether the IAM role requires multi-factor authentication (MFA) | `bool` | `false` | no |
| <a name="input_trusted_role_arns"></a> [trusted\_role\_arns](#input\_trusted\_role\_arns) | The `trusted_role_arns` variable specifies a list of ARNs (Amazon Resource Names) for trusted roles | `list(string)` | <pre>[<br>  ""<br>]</pre> | no |
| <a name="input_user_name"></a> [user\_name](#input\_user\_name) | The `user_name` variable specifies the name of the IAM user | `string` | `"user"` | no |



## Resources




## Contributing

Pull requests are welcome. For major changes, please open an issue first to discuss what you would like to change.
Please make sure to update tests as appropriate.


## License
[MPL](https://choosealicense.com/licenses/mpl-2.0/)
<!-- END OF PRE-COMMIT-TERRAFORM DOCS HOOK -->
