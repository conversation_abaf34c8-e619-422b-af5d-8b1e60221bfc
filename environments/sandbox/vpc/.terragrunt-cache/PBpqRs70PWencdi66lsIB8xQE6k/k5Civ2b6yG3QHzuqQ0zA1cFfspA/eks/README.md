# n5-eks-lightning

<!-- BEGINNING OF PRE-COMMIT-TERRAFORM DOCS HOOK -->
## Scope

The purpose of this module is to enable users to quickly configure an Amazon EKS cluster with the necessary infrastructure on AWS. The module takes care of creating and managing underlying resources, IAM roles and policies, node instances, among others, to simplify the provisioning and configuration process of the EKS cluster.

## Usage

To use this module, input the following variables in your Terraform configuration:

- `cidr_blocks`: The CIDR blocks for the VPC.
- `cluster_name`: The name of the Amazon EKS cluster.
- `vpc_id`: The ID of the VPC where the EKS cluster will be deployed.
- `subnet_ids`: The IDs of the subnets where the EKS nodes will be placed.

Example:
  cidr_blocks   = ["10.0.0.0/16"]
  cluster_name  = "my-eks-cluster"
  vpc_id        = "vpc-12345678"
  subnet_ids    = ["subnet-12345678", "subnet-23456789"]

### Clone project

```hcl
git clone  <repository_url>
cd <project_folder_name>/
## To do plan/apply your code create an PR on Azure DevOps. 
```  
### Installation of pre-commit for Ubuntu 20.04 and higher

```hcl
sudo apt update
sudo apt install -y unzip software-properties-common python3 python3-pip
python3 -m pip install --upgrade pip
pip/pip3 install -r requirements.txt
curl -L "$(curl -s https://api.github.com/repos/terraform-docs/terraform-docs/releases/latest | grep -o -E -m 1 "https://.+?-linux-amd64.tar.gz")" > terraform-docs.tgz && tar -xzf terraform-docs.tgz terraform-docs && rm terraform-docs.tgz && chmod +x terraform-docs && sudo mv terraform-docs /usr/bin/
cd <project_folder_name>/
pre-commit install
```

### Pipeline
From the pipeline you receive the variables:
- creator
- repository
- role_arn
A pipeline is included in this repository in the azure-pipelines.yml file.

Once an PR on main is approved, 3 stages are triggered:
#### terraform-link:
  - terraform_lint
  - terraform fmt -check
  - terraform validate
#### publish_tag:
  - create the tag using versioning configured in the package.json file


### Versioned

Once the project has been created and before working on a new improvement or fix, the following keys must be configured in the package.json file:
- name (project name)
- description (project description)
- type repository (git)
- repository url (repository url)
- version (place the version that will be used for the tag)

See [Conventional Commits](https://www.conventionalcommits.org/en/v1.0.0/)

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | 5.49.0 |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_certificate_authority"></a> [certificate\_authority](#output\_certificate\_authority) | EKS certificate authority |
| <a name="output_cluster_id"></a> [cluster\_id](#output\_cluster\_id) | The `cluster_id` output provides the identifier of the cluster |
| <a name="output_cluster_name"></a> [cluster\_name](#output\_cluster\_name) | The `cluster_name` output provides the name of the cluster |
| <a name="output_cluster_version"></a> [cluster\_version](#output\_cluster\_version) | The `cluster_version` output provides the version of the cluster |
| <a name="output_host"></a> [host](#output\_host) | The `host` output provides EKS cluster endpoint |
| <a name="output_oidc_provider_arn"></a> [oidc\_provider\_arn](#output\_oidc\_provider\_arn) | The `oidc_provider_arn` output provides the Amazon Resource Name (ARN) of the OpenID Connect (OIDC) provider |
| <a name="output_token"></a> [token](#output\_token) | EKS eks token |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_access_entries"></a> [access\_entries](#input\_access\_entries) | The `access_entries` variable provides information about access entries in a security group or network ACL | `any` | `{}` | no |
| <a name="input_cidr_blocks"></a> [cidr\_blocks](#input\_cidr\_blocks) | The `cidr` variable defines the CIDR (Classless Inter-Domain Routing) IP address range for a network | `list(string)` | <pre>[<br>  ""<br>]</pre> | no |
| <a name="input_cluster_addons"></a> [cluster\_addons](#input\_cluster\_addons) | The `cluster_addons` variable specifies the addons to be enabled for the cluster | `any` | <pre>{<br>  "amazon-cloudwatch-observability": {<br>    "most_recent": true<br>  },<br>  "coredns": {<br>    "most_recent": true<br>  },<br>  "kube-proxy": {<br>    "most_recent": true<br>  },<br>  "vpc-cni": {<br>    "most_recent": true<br>  }<br>}</pre> | no |
| <a name="input_cluster_enabled_log_types"></a> [cluster\_enabled\_log\_types](#input\_cluster\_enabled\_log\_types) | The `cluster_enabled_log_types` variable provides the enabled log types for the cluster | `list(string)` | <pre>[<br>  "audit",<br>  "api",<br>  "authenticator",<br>  "scheduler"<br>]</pre> | no |
| <a name="input_cluster_endpoint_private_access"></a> [cluster\_endpoint\_private\_access](#input\_cluster\_endpoint\_private\_access) | The `cluster_endpoint_private_access` variable indicates whether the cluster endpoint has private access | `bool` | `false` | no |
| <a name="input_cluster_endpoint_public_access"></a> [cluster\_endpoint\_public\_access](#input\_cluster\_endpoint\_public\_access) | The `cluster_endpoint_public_access` variable determines whether the cluster's endpoint is publicly accessible | `bool` | `true` | no |
| <a name="input_cluster_name"></a> [cluster\_name](#input\_cluster\_name) | The `cluster_name` variable specifies the name of the Kubernetes cluster | `string` | `""` | no |
| <a name="input_cluster_version"></a> [cluster\_version](#input\_cluster\_version) | The `cluster_version` variable specifies the version of the Kubernetes cluster. | `string` | `"1.28"` | no |
| <a name="input_create_access_entry"></a> [create\_access\_entry](#input\_create\_access\_entry) | The `create_access_entry` variable determines whether to create an access entry | `bool` | `false` | no |
| <a name="input_create_instance_profile"></a> [create\_instance\_profile](#input\_create\_instance\_profile) | The `create_instance_profile` variable determines whether to create an instance profile | `bool` | `false` | no |
| <a name="input_create_node_iam_role"></a> [create\_node\_iam\_role](#input\_create\_node\_iam\_role) | The `create_node_iam_role` variable determines whether to create an IAM role for the nodes | `bool` | `false` | no |
| <a name="input_enable_cluster_creator_admin_permissions"></a> [enable\_cluster\_creator\_admin\_permissions](#input\_enable\_cluster\_creator\_admin\_permissions) | The `enable_cluster_creator_admin_permissions` variable determines whether to enable administrative permissions for the cluster creator | `bool` | `true` | no |
| <a name="input_instance_types"></a> [instance\_types](#input\_instance\_types) | The `instance_types` variable specifies the types of instances to be used | `list(string)` | <pre>[<br>  "r5.xlarge",<br>  "r5.large",<br>  "m5.xlarge",<br>  "m5.large"<br>]</pre> | no |
| <a name="input_karpenter_create_pod_identity_association"></a> [karpenter\_create\_pod\_identity\_association](#input\_karpenter\_create\_pod\_identity\_association) | The `karpenter_enable_pod_identity` variable determines whether to enable pod identity for Karpenter | `bool` | `false` | no |
| <a name="input_karpenter_enable_pod_identity"></a> [karpenter\_enable\_pod\_identity](#input\_karpenter\_enable\_pod\_identity) | value | `bool` | `true` | no |
| <a name="input_karpenter_enabled"></a> [karpenter\_enabled](#input\_karpenter\_enabled) | The `karpenter_enabled` variable determines whether Karpenter, the Kubernetes cluster autoscaler, is enabled | `bool` | `true` | no |
| <a name="input_region"></a> [region](#input\_region) | The `region` variable specifies the AWS region | `string` | `"us-east-1"` | no |
| <a name="input_subnet_ids"></a> [subnet\_ids](#input\_subnet\_ids) | The `subnet_ids` variable specifies the IDs of the private subnets | `list(string)` | `[]` | no |
| <a name="input_tags"></a> [tags](#input\_tags) | The `tags` variable specifies the tags to be applied to resources | `map(string)` | <pre>{<br>  "Owner": "Devops",<br>  "Terraform": "true"<br>}</pre> | no |
| <a name="input_vpc_id"></a> [vpc\_id](#input\_vpc\_id) | The `vpc_id` variable represents the identifier of the Virtual Private Cloud (VPC) | `string` | `""` | no |
| <a name="input_worker_groups_1_block_device_mappings"></a> [worker\_groups\_1\_block\_device\_mappings](#input\_worker\_groups\_1\_block\_device\_mappings) | The `worker_groups_1_block_device_mappings` output specifies the block device mappings for instances in the first worker group | `any` | <pre>{<br>  "root": {<br>    "ebs": {<br>      "volume_type": "gp3"<br>    }<br>  }<br>}</pre> | no |
| <a name="input_worker_groups_1_capacity_type"></a> [worker\_groups\_1\_capacity\_type](#input\_worker\_groups\_1\_capacity\_type) | The `worker_groups_1_capacity_type` variable specifies the capacity type for the first worker group | `string` | `"SPOT"` | no |
| <a name="input_worker_groups_1_desired_size"></a> [worker\_groups\_1\_desired\_size](#input\_worker\_groups\_1\_desired\_size) | The `worker_groups_1_desired_size` variable specifies the desired size of the first worker group | `number` | `1` | no |
| <a name="input_worker_groups_1_disk_size"></a> [worker\_groups\_1\_disk\_size](#input\_worker\_groups\_1\_disk\_size) | The `worker_groups_1_disk_size` output specifies the disk size for instances in the first worker group | `string` | `"100"` | no |
| <a name="input_worker_groups_1_instance_types"></a> [worker\_groups\_1\_instance\_types](#input\_worker\_groups\_1\_instance\_types) | The `worker_groups_1_instance_types` variable specifies the instance types for the first worker group | `list(string)` | <pre>[<br>  "r5.xlarge"<br>]</pre> | no |
| <a name="input_worker_groups_1_max_size"></a> [worker\_groups\_1\_max\_size](#input\_worker\_groups\_1\_max\_size) | The `worker_groups_1_max_size` variable specifies the maximum size of the first worker group | `number` | `10` | no |
| <a name="input_worker_groups_1_min_size"></a> [worker\_groups\_1\_min\_size](#input\_worker\_groups\_1\_min\_size) | The `worker_groups_1_min_size` variable specifies the minimum size of the first worker group | `number` | `1` | no |
| <a name="input_worker_groups_1_name"></a> [worker\_groups\_1\_name](#input\_worker\_groups\_1\_name) | The `worker_groups_1_name` variable specifies the name of the first worker group | `string` | `"worker-group-1"` | no |
| <a name="input_worker_groups_2_block_device_mappings"></a> [worker\_groups\_2\_block\_device\_mappings](#input\_worker\_groups\_2\_block\_device\_mappings) | The `worker_groups_2_block_device_mappings` output specifies the block device mappings for instances in the first worker group | `any` | <pre>{<br>  "root": {<br>    "ebs": {<br>      "volume_type": "gp3"<br>    }<br>  }<br>}</pre> | no |
| <a name="input_worker_groups_2_capacity_type"></a> [worker\_groups\_2\_capacity\_type](#input\_worker\_groups\_2\_capacity\_type) | The `worker_groups_2_capacity_type` variable specifies the capacity type for the second worker group | `string` | `"SPOT"` | no |
| <a name="input_worker_groups_2_desired_size"></a> [worker\_groups\_2\_desired\_size](#input\_worker\_groups\_2\_desired\_size) | The `worker_groups_2_max_size` variable specifies the maximum size of the second worker group | `number` | `1` | no |
| <a name="input_worker_groups_2_disk_size"></a> [worker\_groups\_2\_disk\_size](#input\_worker\_groups\_2\_disk\_size) | The `worker_groups_2_disk_size` output specifies the disk size for instances in the first worker group | `string` | `"100"` | no |
| <a name="input_worker_groups_2_instance_types"></a> [worker\_groups\_2\_instance\_types](#input\_worker\_groups\_2\_instance\_types) | The `worker_groups_2_desired_size` variable specifies the desired size of the second worker group | `list(string)` | <pre>[<br>  "r5.xlarge"<br>]</pre> | no |
| <a name="input_worker_groups_2_max_size"></a> [worker\_groups\_2\_max\_size](#input\_worker\_groups\_2\_max\_size) | The worker\_groups\_2\_max\_size variable specifies the maximum size of the first worker group | `number` | `10` | no |
| <a name="input_worker_groups_2_min_size"></a> [worker\_groups\_2\_min\_size](#input\_worker\_groups\_2\_min\_size) | The `worker_groups_2_min_size` variable specifies the minimum size of the second worker group | `number` | `1` | no |
| <a name="input_worker_groups_2_name"></a> [worker\_groups\_2\_name](#input\_worker\_groups\_2\_name) | The `worker_groups_2_name` variable specifies the name of the second worker group | `string` | `"worker-group-2"` | no |
| <a name="input_worker_groups_3_block_device_mappings"></a> [worker\_groups\_3\_block\_device\_mappings](#input\_worker\_groups\_3\_block\_device\_mappings) | The `worker_groups_3_block_device_mappings` output specifies the block device mappings for instances in the first worker group | `any` | <pre>{<br>  "root": {<br>    "ebs": {<br>      "volume_type": "gp3"<br>    }<br>  }<br>}</pre> | no |
| <a name="input_worker_groups_3_capacity_type"></a> [worker\_groups\_3\_capacity\_type](#input\_worker\_groups\_3\_capacity\_type) | The `worker_groups_3_capacity_type` variable specifies the capacity type for the third worker group | `string` | `"SPOT"` | no |
| <a name="input_worker_groups_3_desired_size"></a> [worker\_groups\_3\_desired\_size](#input\_worker\_groups\_3\_desired\_size) | The `worker_groups_3_desired_size` variable specifies the desired size of the third worker group | `number` | `1` | no |
| <a name="input_worker_groups_3_disk_size"></a> [worker\_groups\_3\_disk\_size](#input\_worker\_groups\_3\_disk\_size) | The `worker_groups_3_disk_size` output specifies the disk size for instances in the first worker group | `string` | `"100"` | no |
| <a name="input_worker_groups_3_instance_types"></a> [worker\_groups\_3\_instance\_types](#input\_worker\_groups\_3\_instance\_types) | The `worker_groups_3_instance_types` variable specifies the instance types for the third worker group | `list(string)` | <pre>[<br>  "r5.xlarge"<br>]</pre> | no |
| <a name="input_worker_groups_3_max_size"></a> [worker\_groups\_3\_max\_size](#input\_worker\_groups\_3\_max\_size) | The `worker_groups_3_max_size` variable specifies the maximum size of the third worker group | `number` | `10` | no |
| <a name="input_worker_groups_3_min_size"></a> [worker\_groups\_3\_min\_size](#input\_worker\_groups\_3\_min\_size) | The `worker_groups_3_min_size` variable specifies the minimum size of the third worker group | `number` | `1` | no |
| <a name="input_worker_groups_3_name"></a> [worker\_groups\_3\_name](#input\_worker\_groups\_3\_name) | The `worker_groups_3_name` variable specifies the name of the third worker group | `string` | `"worker-group-3"` | no |  



## Resources


- resource.aws_iam_role_policy_attachment.AWSXrayWriteOnlyAccess (main.tf#105)
- resource.aws_iam_role_policy_attachment.CloudWatchAgentServerPolicy (main.tf#99)
- data source.aws_eks_cluster.eks (main.tf#136)
- data source.aws_eks_cluster_auth.eks (main.tf#140)  


## Contributing

Pull requests are welcome. For major changes, please open an issue first to discuss what you would like to change.
Please make sure to update tests as appropriate.


## License
[MPL](https://choosealicense.com/licenses/mpl-2.0/)
<!-- END OF PRE-COMMIT-TERRAFORM DOCS HOOK -->
