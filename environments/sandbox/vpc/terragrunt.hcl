include {
  path = find_in_parent_folders("terragrunt.hcl")
}

locals {
  common_vars = yamldecode(file("../common_vars.yaml"))
}

terraform {
  # source = "git::https://dev.azure.com/n5/Platform/_git/fisnky-aws-terraform-modules.git//vpc?ref=v1.0.0"
  # For local development, use:
  source = "../../../../fisnky-aws-terraform-modules//vpc"
}

inputs = {
  # VPC Configuration
  vpc_name = local.common_vars.vpc.name
  cidr     = local.common_vars.vpc.cidr
  
  # Availability Zones and Subnets
  azs             = local.common_vars.vpc.azs
  private_subnets = local.common_vars.vpc.private_subnets
  public_subnets  = local.common_vars.vpc.public_subnets
  
  # NAT Gateway Configuration
  enable_nat_gateway   = true
  single_nat_gateway   = false
  one_nat_gateway_per_az = true
  
  # DNS Configuration
  enable_dns_hostnames = true
  enable_dns_support   = true
  
  # VPN Gateway
  enable_vpn_gateway = false
  
  # Network Address Usage Metrics
  enable_network_address_usage_metrics = true
  
  # Tags
  tags = local.common_vars.tags
  
  # Subnet Tags
  public_subnet_tags = {
    "kubernetes.io/role/elb" = "1"
    "kubernetes.io/cluster/${local.common_vars.aws.eks_name}" = "shared"
  }

  private_subnet_tags = {
    "kubernetes.io/role/internal-elb" = "1"
    "kubernetes.io/cluster/${local.common_vars.aws.eks_name}" = "shared"
  }
  
  nat_gateway_tags = {
    Name = "${local.common_vars.vpc.name}-nat"
  }
  
  # VPC Endpoints
  service_name_s3 = "com.amazonaws.${local.common_vars.aws.region}.s3"
  vpc_endpoint_type_s3 = "Gateway"
  
  service_name_ecr = "com.amazonaws.${local.common_vars.aws.region}.ecr.dkr"
  vpc_endpoint_type_ecr = "Interface"
}
