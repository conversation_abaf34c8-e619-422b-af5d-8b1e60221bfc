# Remote state configuration
state:
  account_name: "Fisnky-Sandbox"
  bucket_name: "fisnky-terraform-state"
  region: "us-east-1"
  encrypt: true
  dynamodb_table: "fisnky-terraform-state-lock"

# AWS configuration
aws:
  aws_account_id: "************"
  region: "us-east-1"
  eks_name: "fisnky-sandbox"
  vpc_id: ""  # Will be populated after VPC creation

# Common configuration
common_name: "fisnky"
env: "sandbox"
project: "fisnky"

# VPC Configuration
vpc:
  name: "fisnky-sandbox"
  cidr: "10.0.0.0/16"
  azs: ["us-east-1a", "us-east-1b", "us-east-1c"]
  private_subnets: ["10.0.1.0/24", "10.0.2.0/24", "10.0.3.0/24"]
  public_subnets: ["10.0.101.0/24", "10.0.102.0/24", "10.0.103.0/24"]

# EKS Configuration
eks:
  cluster_version: "1.28"
  node_groups:
    main:
      instance_types: ["t3.medium", "t3.large"]
      capacity_type: "ON_DEMAND"
      min_size: 1
      max_size: 10
      desired_size: 2

# Tags
tags:
  Environment: "sandbox"
  Project: "fisnky"
  Owner: "DevOps"
  Terraform: "true"
  ManagedBy: "terragrunt"
