variable "environment" {
  description = "Environment name (dev, sandbox, prod, etc.)"
  type        = string
}

variable "tags" {
  description = "Tags to apply to all resources"
  type        = map(string)
  default = {
    "Terraform" = "true"
    "Owner"     = "DevOps"
  }
}

variable "enable_ai_licenses" {
  description = "Enable AI licenses table"
  type        = bool
  default     = true
}

variable "enable_conversations" {
  description = "Enable conversations table"
  type        = bool
  default     = true
}

variable "enable_messages" {
  description = "Enable messages table"
  type        = bool
  default     = true
}

variable "enable_users" {
  description = "Enable users table"
  type        = bool
  default     = true
}

variable "enable_settings" {
  description = "Enable settings table"
  type        = bool
  default     = true
}
