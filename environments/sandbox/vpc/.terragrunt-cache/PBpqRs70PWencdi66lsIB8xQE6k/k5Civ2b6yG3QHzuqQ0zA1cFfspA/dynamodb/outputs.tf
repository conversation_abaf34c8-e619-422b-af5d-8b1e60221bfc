output "ai_licenses_table_name" {
  description = "Name of the AI licenses DynamoDB table"
  value       = aws_dynamodb_table.ai_licenses.name
}

output "ai_licenses_table_arn" {
  description = "ARN of the AI licenses DynamoDB table"
  value       = aws_dynamodb_table.ai_licenses.arn
}

output "conversations_table_name" {
  description = "Name of the conversations DynamoDB table"
  value       = aws_dynamodb_table.conversations.name
}

output "conversations_table_arn" {
  description = "ARN of the conversations DynamoDB table"
  value       = aws_dynamodb_table.conversations.arn
}

output "messages_table_name" {
  description = "Name of the messages DynamoDB table"
  value       = aws_dynamodb_table.messages.name
}

output "messages_table_arn" {
  description = "ARN of the messages DynamoDB table"
  value       = aws_dynamodb_table.messages.arn
}

output "users_table_name" {
  description = "Name of the users DynamoDB table"
  value       = aws_dynamodb_table.users.name
}

output "users_table_arn" {
  description = "ARN of the users DynamoDB table"
  value       = aws_dynamodb_table.users.arn
}

output "settings_table_name" {
  description = "Name of the settings DynamoDB table"
  value       = aws_dynamodb_table.settings.name
}

output "settings_table_arn" {
  description = "ARN of the settings DynamoDB table"
  value       = aws_dynamodb_table.settings.arn
}

output "all_table_arns" {
  description = "List of all DynamoDB table ARNs"
  value = [
    aws_dynamodb_table.ai_licenses.arn,
    aws_dynamodb_table.conversations.arn,
    aws_dynamodb_table.messages.arn,
    aws_dynamodb_table.users.arn,
    aws_dynamodb_table.settings.arn
  ]
}
