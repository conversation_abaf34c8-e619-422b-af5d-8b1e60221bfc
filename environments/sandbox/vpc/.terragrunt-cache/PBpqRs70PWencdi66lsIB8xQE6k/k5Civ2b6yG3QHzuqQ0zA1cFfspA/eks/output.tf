output "cluster_id" {
  value       = module.eks.cluster_id
  description = "The `cluster_id` output provides the identifier of the cluster"
}
output "host" {
  value       = module.eks.cluster_endpoint
  description = "The `host` output provides EKS cluster endpoint"
}

output "oidc_provider_arn" {
  value       = module.eks.oidc_provider_arn
  description = "The `oidc_provider_arn` output provides the Amazon Resource Name (ARN) of the OpenID Connect (OIDC) provider"
}

output "certificate_authority" {
  description = "EKS certificate authority"
  value = base64decode(module.eks.cluster_certificate_authority_data)
}
output "cluster_name" {
  value       = var.cluster_name
  description = "The `cluster_name` output provides the name of the cluster"
}
output "cluster_version" {
  value       = module.eks.cluster_version
  description = "The `cluster_version` output provides the version of the cluster"
}
