resource "aws_iam_role_policy_attachment" "CloudWatchAgentServerPolicy" {
  depends_on = [module.eks]
  role       = module.eks.cluster_iam_role_name
  policy_arn = "arn:aws:iam::aws:policy/CloudWatchAgentServerPolicy"
}

resource "aws_iam_role_policy_attachment" "AWSXrayWriteOnlyAccess" {
  depends_on = [module.eks]
  role       = module.eks.cluster_iam_role_name
  policy_arn = "arn:aws:iam::aws:policy/AWSXrayWriteOnlyAccess"
}

