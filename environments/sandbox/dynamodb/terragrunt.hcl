include {
  path = find_in_parent_folders("terragrunt.hcl")
}

locals {
  common_vars = yamldecode(file("../common_vars.yaml"))
}

# Using inline Terraform configuration for DynamoDB tables
terraform {
  source = "."
}

# Create a simple main.tf file for DynamoDB tables
generate "main" {
  path      = "main.tf"
  if_exists = "overwrite"
  contents  = <<EOF
# DynamoDB Tables for Fisnky Application

resource "aws_dynamodb_table" "ai_licenses" {
  name           = "fisnky-\${var.environment}-ai-licenses"
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "id"

  attribute {
    name = "id"
    type = "S"
  }

  tags = var.tags
}

resource "aws_dynamodb_table" "conversations" {
  name           = "fisnky-\${var.environment}-conversations"
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "id"

  attribute {
    name = "id"
    type = "S"
  }

  attribute {
    name = "user_id"
    type = "S"
  }

  global_secondary_index {
    name     = "user-index"
    hash_key = "user_id"
  }

  tags = var.tags
}

resource "aws_dynamodb_table" "messages" {
  name           = "fisnky-\${var.environment}-messages"
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "conversation_id"
  range_key      = "timestamp"

  attribute {
    name = "conversation_id"
    type = "S"
  }

  attribute {
    name = "timestamp"
    type = "S"
  }

  tags = var.tags
}

resource "aws_dynamodb_table" "users" {
  name           = "fisnky-\${var.environment}-users"
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "id"

  attribute {
    name = "id"
    type = "S"
  }

  attribute {
    name = "email"
    type = "S"
  }

  global_secondary_index {
    name     = "email-index"
    hash_key = "email"
  }

  tags = var.tags
}

resource "aws_dynamodb_table" "settings" {
  name           = "fisnky-${var.environment}-settings"
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "key"

  attribute {
    name = "key"
    type = "S"
  }

  tags = var.tags
}
EOF
}

# Create variables.tf file
generate "variables" {
  path      = "variables.tf"
  if_exists = "overwrite"
  contents  = <<EOF
variable "environment" {
  description = "Environment name"
  type        = string
}

variable "tags" {
  description = "Tags to apply to resources"
  type        = map(string)
  default     = {}
}
EOF
}

# Create outputs.tf file
generate "outputs" {
  path      = "outputs.tf"
  if_exists = "overwrite"
  contents  = <<EOF
output "ai_licenses_table_name" {
  description = "Name of the AI licenses table"
  value       = aws_dynamodb_table.ai_licenses.name
}

output "ai_licenses_table_arn" {
  description = "ARN of the AI licenses table"
  value       = aws_dynamodb_table.ai_licenses.arn
}

output "conversations_table_name" {
  description = "Name of the conversations table"
  value       = aws_dynamodb_table.conversations.name
}

output "conversations_table_arn" {
  description = "ARN of the conversations table"
  value       = aws_dynamodb_table.conversations.arn
}

output "messages_table_name" {
  description = "Name of the messages table"
  value       = aws_dynamodb_table.messages.name
}

output "messages_table_arn" {
  description = "ARN of the messages table"
  value       = aws_dynamodb_table.messages.arn
}

output "users_table_name" {
  description = "Name of the users table"
  value       = aws_dynamodb_table.users.name
}

output "users_table_arn" {
  description = "ARN of the users table"
  value       = aws_dynamodb_table.users.arn
}

output "settings_table_name" {
  description = "Name of the settings table"
  value       = aws_dynamodb_table.settings.name
}

output "settings_table_arn" {
  description = "ARN of the settings table"
  value       = aws_dynamodb_table.settings.arn
}
EOF
}

inputs = {
  environment = local.common_vars.env
  tags        = local.common_vars.tags
}
