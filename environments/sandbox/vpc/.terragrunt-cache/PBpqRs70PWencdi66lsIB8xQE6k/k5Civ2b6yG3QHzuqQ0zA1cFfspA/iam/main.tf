## AWS Provider
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "5.37.0" # Cambiar por la versión necesaria.
    }
  }
}

module "iam_policy" {
  source  = "terraform-aws-modules/iam/aws//modules/iam-policy"
  version = "5.38.0"

  name          = var.policy_name
  create_policy = var.create_policy
  path          = var.path
  description   = var.policy_description
  policy        = jsonencode(var.policies)
}

module "iam_assumable_role" {
  source  = "terraform-aws-modules/iam/aws//modules/iam-assumable-role"
  version = "5.38.0"

  trusted_role_arns                 = var.trusted_role_arns
  create_role                       = var.create_role
  role_name                         = var.role_name
  role_requires_mfa                 = var.role_requires_mfa
  custom_role_policy_arns           = [module.iam_policy.arn]
  number_of_custom_role_policy_arns = 1
}

module "iam_assumable_role_with_oidc" {
  source = "terraform-aws-modules/iam/aws//modules/iam-assumable-role-with-oidc"

  create_role = var.create_role_oidc

  role_name = var.role_name_oidc

  provider_url = var.provider_url_oidc

  role_policy_arns = [module.iam_policy.arn]

  number_of_role_policy_arns = 1
}

module "iam_user" {
  source  = "terraform-aws-modules/iam/aws//modules/iam-user"
  version = "5.38.0"

  name                    = var.user_name
  create_user             = var.create_user
  force_destroy           = var.force_destroy
  create_iam_access_key   = var.create_iam_access_key
  password_reset_required = var.password_reset_required
  policy_arns             = [module.iam_policy.arn]
}