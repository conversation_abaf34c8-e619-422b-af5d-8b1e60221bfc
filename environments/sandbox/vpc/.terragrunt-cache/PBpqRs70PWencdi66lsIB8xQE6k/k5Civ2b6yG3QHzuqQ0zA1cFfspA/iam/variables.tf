variable "region" {
  type        = string
  default     = "us-east-1"
  description = "AWS region"
}
variable "create_policy" {
  type    = bool
  default = false
}

variable "create_user" {
  type    = bool
  default = false
}

variable "policy_name" {
  type        = string
  default     = "policy"
  description = "The `policy_name` variable specifies the name of the policy"
}

variable "path" {
  type        = string
  default     = "/"
  description = "The `path` variable specifies the path for a resource in AWS Identity and Access Management (IAM)"
}

variable "policy_description" {
  type        = string
  default     = "policy_description"
  description = "The `policy_description` variable provides a description for the IAM policy"
}

variable "policies" {
  type = any
  default = {
    "Version" : "2012-10-17",
    "Statement" : [
      {
        "Sid" : "VisualEditor0",
        "Effect" : "Allow",
        "Action" : "sts:AssumeRole",
        "Resource" : [
          "arn:aws:iam:::role/"
        ]
      }
    ]
  }
  description = "The `policies` variable is used to define a list of IAM policies to be attached to a resource"
}

variable "trusted_role_arns" {
  type        = list(string)
  default     = [""]
  description = "The `trusted_role_arns` variable specifies a list of ARNs (Amazon Resource Names) for trusted roles"
}

variable "create_role" {
  type        = bool
  default     = true
  description = "The `create_role` variable determines whether to create a new IAM role"
}

variable "role_name" {
  type        = string
  default     = "role"
  description = "The `role_name` variable specifies the name of the IAM role"
}

variable "role_requires_mfa" {
  type        = bool
  default     = false
  description = "The `role_requires_mfa` variable determines whether the IAM role requires multi-factor authentication (MFA)"
}

variable "user_name" {
  type        = string
  default     = "user"
  description = "The `user_name` variable specifies the name of the IAM user"
}

variable "force_destroy" {
  type        = bool
  default     = true
  description = "The `force_destroy` variable specifies whether to force the destruction of the resource, even if it contains data"
}

variable "create_iam_access_key" {
  type        = bool
  default     = true
  description = "The `create_iam_access_key` variable determines whether to create an IAM access key for the user"
}

variable "password_reset_required" {
  type        = bool
  default     = false
  description = "The `password_reset_required` variable specifies whether the IAM user is required to reset their password at the next sign-in"
}

variable "create_role_oidc" {
  type        = bool
  default     = false
  description = "The `create_role_oidc` variable determines whether to create an IAM role with an OpenID Connect (OIDC) provider"
}

variable "role_name_oidc" {
  type        = string
  default     = "role_oidc"
  description = "The `role_name_oidc` variable specifies the name of the IAM role associated with the OpenID Connect (OIDC) provider"
}

variable "provider_url_oidc" {
  type        = string
  default     = "oidc.eks.eu-west-1.amazonaws.com/id/000000000000000000000000"
  description = "The `provider_url_oidc` variable specifies the URL of the OpenID Connect (OIDC) provider"
}