include {
  path = find_in_parent_folders("terragrunt.hcl")
}

locals {
  common_vars = yamldecode(file("../common_vars.yaml"))
}

terraform {
  # Using public Terraform Registry module for EKS
  source = "tfr:///terraform-aws-modules/eks/aws?version=20.31.0"
  # Original private module (requires Azure DevOps auth):
  # source = "git::https://dev.azure.com/n5/Platform/_git/fisnky-aws-terraform-modules.git//eks?ref=v1.0.0"
}

# Dependencies
dependencies {
  paths = ["../vpc"]
}

dependency "vpc" {
  config_path = "../vpc"
  mock_outputs = {
    vpc_id          = "vpc-12345678"
    private_subnets = ["subnet-12345678", "subnet-87654321", "subnet-11111111"]
    public_subnets  = ["subnet-99999999", "subnet-88888888", "subnet-77777777"]
    vpc_cidr_block  = "10.0.0.0/16"
  }
  skip_outputs = false
}

inputs = {
  # Cluster Configuration
  cluster_name    = local.common_vars.aws.eks_name
  cluster_version = local.common_vars.eks.cluster_version
  
  # Network Configuration
  vpc_id     = dependency.vpc.outputs.vpc_id
  subnet_ids = dependency.vpc.outputs.private_subnets
  
  # Cluster Addons
  cluster_addons = {
    coredns = {
      most_recent = true
    }
    kube-proxy = {
      most_recent = true
    }
    vpc-cni = {
      most_recent = true
    }
    aws-ebs-csi-driver = {
      most_recent = true
    }
  }
  
  # Logging
  cluster_enabled_log_types = ["api", "audit", "authenticator", "controllerManager", "scheduler"]
  cloudwatch_log_group_retention_in_days = 30
  
  # Endpoint Configuration
  cluster_endpoint_public_access  = true
  cluster_endpoint_private_access = true
  cluster_endpoint_public_access_cidrs = ["0.0.0.0/0"]
  
  # IRSA
  enable_irsa = true
  enable_cluster_creator_admin_permissions = true
  
  # Karpenter
  karpenter_enabled = true
  karpenter_enable_pod_identity = true
  karpenter_create_pod_identity_association = false
  
  # Node Groups
  eks_managed_node_groups = {
    main = {
      name = "${local.common_vars.aws.eks_name}-main"
      
      instance_types = local.common_vars.eks.node_groups.main.instance_types
      capacity_type  = local.common_vars.eks.node_groups.main.capacity_type
      
      min_size     = local.common_vars.eks.node_groups.main.min_size
      max_size     = local.common_vars.eks.node_groups.main.max_size
      desired_size = local.common_vars.eks.node_groups.main.desired_size
      
      # Additional policies
      iam_role_additional_policies = {
        AmazonSSMManagedInstanceCore = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
      }
      
      # Labels
      labels = {
        Environment = local.common_vars.env
        NodeGroup   = "main"
      }
      
      # Taints
      taints = []
      
      # Tags
      tags = local.common_vars.tags
    }
  }
  
  # Access Entries (replace aws-auth ConfigMap)
  access_entries = {
    admin = {
      principal_arn = "arn:aws:iam::${local.common_vars.aws.aws_account_id}:root"

      policy_associations = {
        admin = {
          policy_arn = "arn:aws:eks::aws:cluster-access-policy/AmazonEKSClusterAdminPolicy"
          access_scope = {
            type = "cluster"
          }
        }
      }
    }
  }
  
  # Tags
  tags = local.common_vars.tags
}
