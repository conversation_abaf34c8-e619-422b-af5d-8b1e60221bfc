locals {
  common_vars = yamldecode(file("common_vars.yaml"))
}

# Remote state configuration
remote_state {
  backend = "s3"
  generate = {
    path      = "backend.tf"
    if_exists = "overwrite"
  }
  config = {
    bucket         = local.common_vars.state.bucket_name
    key            = "${local.common_vars.state.account_name}/${local.common_vars.common_name}/${local.common_vars.env}/${path_relative_to_include()}/terraform.tfstate"
    region         = local.common_vars.state.region
    encrypt        = local.common_vars.state.encrypt
    dynamodb_table = local.common_vars.state.dynamodb_table
    
    # Uncomment and configure if using cross-account access
    # assume_role = {
    #   role_arn = "arn:aws:iam::${local.common_vars.aws.aws_account_id}:role/TerraformRole"
    # }
  }
}

# Generate versions.tf
generate "versions" {
  path      = "versions.tf"
  if_exists = "overwrite"
  contents  = file("../../globals/versions.tf")
}

# Generate provider configuration
generate "provider" {
  path      = "provider.tf"
  if_exists = "overwrite"
  contents  = <<EOF
provider "aws" {
  region = "${local.common_vars.aws.region}"
  
  # Uncomment if using cross-account access
  # assume_role {
  #   role_arn = "arn:aws:iam::${local.common_vars.aws.aws_account_id}:role/TerraformRole"
  # }

  default_tags {
    tags = {
      Environment = "${local.common_vars.env}"
      Project     = "${local.common_vars.project}"
      Owner       = "DevOps"
      Terraform   = "true"
      ManagedBy   = "terragrunt"
    }
  }
}
EOF
}
