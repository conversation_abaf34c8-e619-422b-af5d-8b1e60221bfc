output "indexing_queue_url" {
  description = "URL of the indexing SQS queue"
  value       = aws_sqs_queue.indexing_queue.url
}

output "indexing_queue_arn" {
  description = "ARN of the indexing SQS queue"
  value       = aws_sqs_queue.indexing_queue.arn
}

output "indexing_dlq_url" {
  description = "URL of the indexing DLQ"
  value       = aws_sqs_queue.indexing_dlq.url
}

output "indexing_dlq_arn" {
  description = "ARN of the indexing DLQ"
  value       = aws_sqs_queue.indexing_dlq.arn
}

output "usage_queue_url" {
  description = "URL of the usage tracking SQS queue"
  value       = aws_sqs_queue.usage_queue.url
}

output "usage_queue_arn" {
  description = "ARN of the usage tracking SQS queue"
  value       = aws_sqs_queue.usage_queue.arn
}

output "usage_dlq_url" {
  description = "URL of the usage DLQ"
  value       = aws_sqs_queue.usage_dlq.url
}

output "usage_dlq_arn" {
  description = "ARN of the usage DLQ"
  value       = aws_sqs_queue.usage_dlq.arn
}

output "notifications_queue_url" {
  description = "URL of the notifications SQS queue"
  value       = aws_sqs_queue.notifications_queue.url
}

output "notifications_queue_arn" {
  description = "ARN of the notifications SQS queue"
  value       = aws_sqs_queue.notifications_queue.arn
}

output "notifications_dlq_url" {
  description = "URL of the notifications DLQ"
  value       = aws_sqs_queue.notifications_dlq.url
}

output "notifications_dlq_arn" {
  description = "ARN of the notifications DLQ"
  value       = aws_sqs_queue.notifications_dlq.arn
}

output "all_queue_arns" {
  description = "List of all SQS queue ARNs"
  value = [
    aws_sqs_queue.indexing_queue.arn,
    aws_sqs_queue.indexing_dlq.arn,
    aws_sqs_queue.usage_queue.arn,
    aws_sqs_queue.usage_dlq.arn,
    aws_sqs_queue.notifications_queue.arn,
    aws_sqs_queue.notifications_dlq.arn
  ]
}
