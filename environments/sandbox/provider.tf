# Generated by Terragrunt. Sig: nIlQXj57tbuaRZEa
provider "aws" {
  region = "us-east-1"

  # Uncomment if using cross-account access
  # assume_role {
  #   role_arn = "arn:aws:iam::************:role/TerraformRole"
  # }

  default_tags {
    tags = {
      Environment = "sandbox"
      Project     = "fisnky"
      Owner       = "DevOps"
      Terraform   = "true"
      ManagedBy   = "terragrunt"
    }
  }
}
