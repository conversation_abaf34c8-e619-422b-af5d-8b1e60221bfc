variable "region" {
  type        = string
  description = "AWS region"
  default     = "us-east-1"
}
variable "vpc_name" {
  type        = string
  default     = ""
  description = "The `vpc_name` variable indicates the name of the VPC"
}
variable "cidr" {
  type        = string
  default     = ""
  description = "The `cidr` variable defines the CIDR (Classless Inter-Domain Routing) IP address range for a network"
}
variable "azs" {
  type        = list(string)
  default     = []
  description = "The `azs` variable specifies the Availability Zones to be used in Terraform"
}
variable "private_subnets" {
  type        = list(string)
  default     = []
  description = "The `private_subnets` variable defines the private subnets"
}
variable "public_subnets" {
  type        = list(string)
  default     = []
  description = "The `public_subnets` variable defines the public subnets"
}
variable "enable_nat_gateway" {
  type        = bool
  default     = true
  description = "The `enable_nat_gateway` variable determines whether to enable NAT gateway functionality"
}
variable "single_nat_gateway" {
  type        = bool
  default     = false
  description = "The `single_nat_gateway` variable specifies whether to use a single NAT gateway"
}
variable "enable_dns_hostnames" {
  type        = bool
  default     = true
  description = "The `enable_dns_hostnames` variable determines whether DNS hostnames are enabled"
}
variable "enable_dns_support" {
  type        = bool
  default     = true
  description = "The `enable_dns_support` variable determines whether DNS support is enabled"
}
variable "one_nat_gateway_per_az" {
  type        = bool
  default     = true
  description = "The `one_nat_gateway_per_az` variable specifies whether to provision one NAT gateway per Availability Zone"
}
variable "enable_vpn_gateway" {
  type        = bool
  default     = false
  description = "The `enable_vpn_gateway` variable specifies whether to enable a VPN gateway"
}
variable "tags" {
  description = "The `tags` variable specifies the tags to be applied to resources"
  type        = map(string)
  default = {
    "Owner"     = "Devops"
    "Terraform" = "true"
  }
}
variable "public_subnet_tags" {
  description = "The `public_subnet_tags` variable defines the tags to be applied to public subnets"
  type        = map(string)
  default = {
    "Owner"     = "Devops"
    "Terraform" = "true"
  }
}
variable "private_subnet_tags" {
  description = "The `private_subnet_tags` variable defines the tags to be applied to private subnets"
  type        = map(string)
  default = {
    "Owner"     = "Devops"
    "Terraform" = "true"
  }
}
variable "nat_gateway_tags" {
  description = "The `nat_gateway_tags` variable defines the tags to be applied to NAT gateways"
  type        = map(string)
  default = {
    "Owner"     = "Devops"
    "Terraform" = "true"
  }
}
variable "service_name_s3" {
  description = "The `service_name_s3` variable refers to the service name within the AWS endpoint for S3"
  type        = string
  default     = "com.amazonaws.us-east-1.s3"
}
variable "vpc_endpoint_type_s3" {
  description = "The `vpc_endpoint_type_s3` variable specifies the type of VPC endpoint for S3"
  type        = string
  default     = "Gateway"
}
variable "service_name_ecr" {
  description = "The `service_name_ecr` variable refers to the service name within the AWS endpoint for ECR"
  type        = string
  default     = "com.amazonaws.us-east-1.ecr.dkr"
}
variable "vpc_endpoint_type_ecr" {
  description = "The `vpc_endpoint_type_ecr` variable specifies the type of VPC endpoint for ECR"
  type        = string
  default     = "Interface"
}

variable "enable_network_address_usage_metrics" {
  description = "Determines whether network address usage metrics are enabled for the VPC"
  type        = bool
  default     = true
}