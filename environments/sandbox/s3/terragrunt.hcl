include {
  path = find_in_parent_folders("terragrunt.hcl")
}

locals {
  common_vars = yamldecode(file("../common_vars.yaml"))
}

terraform {
  source = "git::https://dev.azure.com/n5/Platform/_git/fisnky-aws-terraform-modules.git//s3?ref=v1.0.0"
  # For local development, use:
  # source = "../../../fisnky-aws-terraform-modules//s3"
}

inputs = {
  # Environment
  environment = local.common_vars.env
  
  # Enable/Disable buckets
  enable_ai_core_models  = true
  enable_file_repository = true
  enable_backups         = true
  enable_logs            = true
  
  # Tags
  tags = local.common_vars.tags
}
