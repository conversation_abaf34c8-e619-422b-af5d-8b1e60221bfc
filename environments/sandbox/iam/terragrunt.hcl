include {
  path = find_in_parent_folders("terragrunt.hcl")
}

locals {
  common_vars = yamldecode(file("../common_vars.yaml"))
}

terraform {
  source = "git::https://dev.azure.com/n5/Platform/_git/fisnky-aws-terraform-modules.git//iam?ref=v1.0.0"
  # For local development, use:
  # source = "../../../fisnky-aws-terraform-modules//iam"
}

# Dependencies
dependencies {
  paths = ["../eks", "../dynamodb", "../sqs", "../s3"]
}

dependency "eks" {
  config_path = "../eks"
  mock_outputs = {
    cluster_oidc_issuer_url = "https://oidc.eks.us-east-1.amazonaws.com/id/EXAMPLE"
  }
  skip_outputs = false
}

dependency "dynamodb" {
  config_path = "../dynamodb"
  mock_outputs = {
    all_table_arns = ["arn:aws:dynamodb:us-east-1:************:table/example"]
  }
  skip_outputs = false
}

dependency "sqs" {
  config_path = "../sqs"
  mock_outputs = {
    all_queue_arns = ["arn:aws:sqs:us-east-1:************:example"]
  }
  skip_outputs = false
}

dependency "s3" {
  config_path = "../s3"
  mock_outputs = {
    all_bucket_arns = ["arn:aws:s3:::example"]
  }
  skip_outputs = false
}

inputs = {
  # Environment
  environment = local.common_vars.env
  
  # EKS OIDC Provider
  cluster_oidc_issuer_url = dependency.eks.outputs.cluster_oidc_issuer_url
  
  # Service Account Configurations
  service_accounts = {
    ai_core_api = {
      namespace = "ai-core"
      name      = "ai-core-api"
      policies = [
        {
          name = "ai-core-api-policy"
          policy = {
            Version = "2012-10-17"
            Statement = [
              {
                Effect = "Allow"
                Action = [
                  "dynamodb:GetItem",
                  "dynamodb:PutItem",
                  "dynamodb:UpdateItem",
                  "dynamodb:DeleteItem",
                  "dynamodb:Query",
                  "dynamodb:Scan"
                ]
                Resource = dependency.dynamodb.outputs.all_table_arns
              },
              {
                Effect = "Allow"
                Action = [
                  "sqs:SendMessage",
                  "sqs:ReceiveMessage",
                  "sqs:DeleteMessage",
                  "sqs:GetQueueAttributes"
                ]
                Resource = dependency.sqs.outputs.all_queue_arns
              },
              {
                Effect = "Allow"
                Action = [
                  "s3:GetObject",
                  "s3:PutObject",
                  "s3:DeleteObject"
                ]
                Resource = [
                  for arn in dependency.s3.outputs.all_bucket_arns : "${arn}/*"
                ]
              }
            ]
          }
        }
      ]
    }
    
    ai_core_indexer = {
      namespace = "ai-core"
      name      = "ai-core-indexer"
      policies = [
        {
          name = "ai-core-indexer-policy"
          policy = {
            Version = "2012-10-17"
            Statement = [
              {
                Effect = "Allow"
                Action = [
                  "dynamodb:GetItem",
                  "dynamodb:PutItem",
                  "dynamodb:UpdateItem",
                  "dynamodb:DeleteItem",
                  "dynamodb:Query",
                  "dynamodb:Scan"
                ]
                Resource = dependency.dynamodb.outputs.all_table_arns
              },
              {
                Effect = "Allow"
                Action = [
                  "sqs:SendMessage",
                  "sqs:ReceiveMessage",
                  "sqs:DeleteMessage",
                  "sqs:GetQueueAttributes"
                ]
                Resource = dependency.sqs.outputs.all_queue_arns
              },
              {
                Effect = "Allow"
                Action = [
                  "s3:GetObject",
                  "s3:PutObject",
                  "s3:DeleteObject",
                  "s3:ListBucket"
                ]
                Resource = concat(
                  dependency.s3.outputs.all_bucket_arns,
                  [for arn in dependency.s3.outputs.all_bucket_arns : "${arn}/*"]
                )
              }
            ]
          }
        }
      ]
    }
  }
  
  # Tags
  tags = local.common_vars.tags
}
