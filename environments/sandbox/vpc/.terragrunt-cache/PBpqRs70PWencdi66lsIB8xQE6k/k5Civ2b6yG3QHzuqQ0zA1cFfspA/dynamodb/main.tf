terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

# DynamoDB Tables for AI Core services
resource "aws_dynamodb_table" "ai_licenses" {
  name           = "${var.environment}-ai-licenses"
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "id"

  attribute {
    name = "id"
    type = "S"
  }

  tags = merge(var.tags, {
    Name = "${var.environment}-ai-licenses"
    Service = "ai-core"
  })
}

resource "aws_dynamodb_table" "conversations" {
  name           = "${var.environment}-conversations"
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "conversation_id"

  attribute {
    name = "conversation_id"
    type = "S"
  }

  attribute {
    name = "group_id"
    type = "S"
  }

  attribute {
    name = "created_at"
    type = "S"
  }

  global_secondary_index {
    name     = "GROUP_ID_CREATED_AT_IDX"
    hash_key = "group_id"
    range_key = "created_at"
  }

  tags = merge(var.tags, {
    Name = "${var.environment}-conversations"
    Service = "ai-core"
  })
}

resource "aws_dynamodb_table" "messages" {
  name           = "${var.environment}-messages"
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "message_id"

  attribute {
    name = "message_id"
    type = "S"
  }

  attribute {
    name = "conversation_id"
    type = "S"
  }

  local_secondary_index {
    name      = "CONVERSATION_ID_MESSAGE_IDX"
    range_key = "message_id"
  }

  tags = merge(var.tags, {
    Name = "${var.environment}-messages"
    Service = "ai-core"
  })
}

resource "aws_dynamodb_table" "users" {
  name           = "${var.environment}-users"
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "user_id"

  attribute {
    name = "user_id"
    type = "S"
  }

  tags = merge(var.tags, {
    Name = "${var.environment}-users"
    Service = "users-management"
  })
}

resource "aws_dynamodb_table" "settings" {
  name           = "${var.environment}-settings"
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "setting_id"

  attribute {
    name = "setting_id"
    type = "S"
  }

  tags = merge(var.tags, {
    Name = "${var.environment}-settings"
    Service = "settings"
  })
}
