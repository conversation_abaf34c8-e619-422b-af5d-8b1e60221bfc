# n5-vpc-lightning

<!-- BEGINNING OF PRE-COMMIT-TERRAFORM DOCS HOOK -->
## Scope


## Usage

### Clone project

```hcl
git clone  <repository_url>
cd <project_folder_name>/
## To do plan/apply your code create an PR on Azure DevOps.
```
### Installation of pre-commit for Ubuntu 20.04 and higher

```hcl
sudo apt update
sudo apt install -y unzip software-properties-common python3 python3-pip
python3 -m pip install --upgrade pip
pip/pip3 install -r requirements.txt
curl -L "$(curl -s https://api.github.com/repos/terraform-docs/terraform-docs/releases/latest | grep -o -E -m 1 "https://.+?-linux-amd64.tar.gz")" > terraform-docs.tgz && tar -xzf terraform-docs.tgz terraform-docs && rm terraform-docs.tgz && chmod +x terraform-docs && sudo mv terraform-docs /usr/bin/
cd <project_folder_name>/
pre-commit install
```

### Pipeline
From the pipeline you receive the variables:
- creator
- repository
- role_arn
A pipeline is included in this repository in the azure-pipelines.yml file.

Once an PR on main is approved, 3 stages are triggered:
#### terraform-link:
- terraform_lint
- terraform fmt -check
- terraform validate
#### publish_tag:
- create the tag using versioning configured in the package.json file


### Versioned

Once the project has been created and before working on a new improvement or fix, the following keys must be configured in the package.json file:
- name (project name)
- description (project description)
- type repository (git)
- repository url (repository url)
- version (place the version that will be used for the tag)

See [Conventional Commits](https://www.conventionalcommits.org/en/v1.0.0/)

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | 5.37.0 |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_private_subnets"></a> [private\_subnets](#output\_private\_subnets) | The `private_subnets` output provides the private subnets |
| <a name="output_public_subnets"></a> [public\_subnets](#output\_public\_subnets) | The `public_subnets` variable specifies the public subnets |
| <a name="output_vpc_cidr_block"></a> [vpc\_cidr\_block](#output\_vpc\_cidr\_block) | The `vpc_cidr_block` variable defines the CIDR block for the Virtual Private Cloud (VPC) |
| <a name="output_vpc_id"></a> [vpc\_id](#output\_vpc\_id) | The `vpc_id` output provides the identifier of the Virtual Private Cloud (VPC). |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_azs"></a> [azs](#input\_azs) | The `azs` variable specifies the Availability Zones to be used in Terraform | `list(string)` | `[]` | no |
| <a name="input_cidr"></a> [cidr](#input\_cidr) | The `cidr` variable defines the CIDR (Classless Inter-Domain Routing) IP address range for a network | `string` | `""` | no |
| <a name="input_enable_dns_hostnames"></a> [enable\_dns\_hostnames](#input\_enable\_dns\_hostnames) | The `enable_dns_hostnames` variable determines whether DNS hostnames are enabled | `bool` | `true` | no |
| <a name="input_enable_dns_support"></a> [enable\_dns\_support](#input\_enable\_dns\_support) | The `enable_dns_support` variable determines whether DNS support is enabled | `bool` | `true` | no |
| <a name="input_enable_nat_gateway"></a> [enable\_nat\_gateway](#input\_enable\_nat\_gateway) | The `enable_nat_gateway` variable determines whether to enable NAT gateway functionality | `bool` | `true` | no |
| <a name="input_enable_vpn_gateway"></a> [enable\_vpn\_gateway](#input\_enable\_vpn\_gateway) | The `enable_vpn_gateway` variable specifies whether to enable a VPN gateway | `bool` | `false` | no |
| <a name="input_nat_gateway_tags"></a> [nat\_gateway\_tags](#input\_nat\_gateway\_tags) | The `nat_gateway_tags` variable defines the tags to be applied to NAT gateways | `map(string)` | <pre>{<br>  "Owner": "Devops",<br>  "Terraform": "true"<br>}</pre> | no |
| <a name="input_one_nat_gateway_per_az"></a> [one\_nat\_gateway\_per\_az](#input\_one\_nat\_gateway\_per\_az) | The `one_nat_gateway_per_az` variable specifies whether to provision one NAT gateway per Availability Zone | `bool` | `true` | no |
| <a name="input_private_subnet_tags"></a> [private\_subnet\_tags](#input\_private\_subnet\_tags) | The `private_subnet_tags` variable defines the tags to be applied to private subnets | `map(string)` | <pre>{<br>  "Owner": "Devops",<br>  "Terraform": "true"<br>}</pre> | no |
| <a name="input_private_subnets"></a> [private\_subnets](#input\_private\_subnets) | The `private_subnets` variable defines the private subnets | `list(string)` | `[]` | no |
| <a name="input_public_subnet_tags"></a> [public\_subnet\_tags](#input\_public\_subnet\_tags) | The `public_subnet_tags` variable defines the tags to be applied to public subnets | `map(string)` | <pre>{<br>  "Owner": "Devops",<br>  "Terraform": "true"<br>}</pre> | no |
| <a name="input_public_subnets"></a> [public\_subnets](#input\_public\_subnets) | The `public_subnets` variable defines the public subnets | `list(string)` | `[]` | no |
| <a name="input_region"></a> [region](#input\_region) | AWS region | `string` | `"us-east-1"` | no |
| <a name="input_service_name_ecr"></a> [service\_name\_ecr](#input\_service\_name\_ecr) | The `service_name_ecr` variable refers to the service name within the AWS endpoint for ECR | `string` | `"com.amazonaws.us-east-1.ecr.dkr"` | no |
| <a name="input_service_name_s3"></a> [service\_name\_s3](#input\_service\_name\_s3) | The `service_name_s3` variable refers to the service name within the AWS endpoint for S3 | `string` | `"com.amazonaws.us-east-1.s3"` | no |
| <a name="input_single_nat_gateway"></a> [single\_nat\_gateway](#input\_single\_nat\_gateway) | The `single_nat_gateway` variable specifies whether to use a single NAT gateway | `bool` | `false` | no |
| <a name="input_tags"></a> [tags](#input\_tags) | The `tags` variable specifies the tags to be applied to resources | `map(string)` | <pre>{<br>  "Owner": "Devops",<br>  "Terraform": "true"<br>}</pre> | no |
| <a name="input_vpc_endpoint_type_ecr"></a> [vpc\_endpoint\_type\_ecr](#input\_vpc\_endpoint\_type\_ecr) | The `vpc_endpoint_type_ecr` variable specifies the type of VPC endpoint for ECR | `string` | `"Interface"` | no |
| <a name="input_vpc_endpoint_type_s3"></a> [vpc\_endpoint\_type\_s3](#input\_vpc\_endpoint\_type\_s3) | The `vpc_endpoint_type_s3` variable specifies the type of VPC endpoint for S3 | `string` | `"Gateway"` | no |
| <a name="input_vpc_name"></a> [vpc\_name](#input\_vpc\_name) | The `vpc_name` variable indicates the name of the VPC | `string` | `""` | no |



## Resources


- resource.aws_vpc_endpoint.ecr (main.tf#53)
- resource.aws_vpc_endpoint.s3 (main.tf#45)


## Contributing

Pull requests are welcome. For major changes, please open an issue first to discuss what you would like to change.
Please make sure to update tests as appropriate.


## License
[MPL](https://choosealicense.com/licenses/mpl-2.0/)
<!-- END OF PRE-COMMIT-TERRAFORM DOCS HOOK -->
