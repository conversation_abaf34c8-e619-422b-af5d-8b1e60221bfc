variable "environment" {
  description = "Environment name (dev, sandbox, prod, etc.)"
  type        = string
}

variable "tags" {
  description = "Tags to apply to all resources"
  type        = map(string)
  default = {
    "Terraform" = "true"
    "Owner"     = "DevOps"
  }
}

variable "enable_ai_core_models" {
  description = "Enable AI core models bucket"
  type        = bool
  default     = true
}

variable "enable_file_repository" {
  description = "Enable file repository bucket"
  type        = bool
  default     = true
}

variable "enable_backups" {
  description = "Enable backups bucket"
  type        = bool
  default     = true
}

variable "enable_logs" {
  description = "Enable logs bucket"
  type        = bool
  default     = true
}
