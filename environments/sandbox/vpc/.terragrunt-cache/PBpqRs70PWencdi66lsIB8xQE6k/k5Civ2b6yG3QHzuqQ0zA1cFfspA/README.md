# Fisnky AWS Terraform Modules

This repository contains reusable Terraform modules for Fisnky project infrastructure. These modules are designed to be used with Terragrunt for deployment orchestration.

## 📁 Repository Structure

```
fisnky-aws-terraform-modules/
├── vpc/                    # VPC with subnets, NAT gateways, VPC endpoints
│   ├── main.tf
│   ├── variables.tf
│   └── outputs.tf
├── eks/                    # EKS cluster with managed node groups
│   ├── main.tf
│   ├── variables.tf
│   └── outputs.tf
├── iam/                    # IAM roles and policies for service accounts
│   ├── main.tf
│   ├── variables.tf
│   └── outputs.tf
├── dynamodb/               # DynamoDB tables for application data
│   ├── main.tf
│   ├── variables.tf
│   └── outputs.tf
├── sqs/                    # SQS queues for async processing
│   ├── main.tf
│   ├── variables.tf
│   └── outputs.tf
└── s3/                     # S3 buckets for file storage
    ├── main.tf
    ├── variables.tf
    └── outputs.tf
```

## 🏗️ Available Modules

### VPC Module (`vpc/`)
Creates a complete VPC infrastructure with:
- Public and private subnets across multiple AZs
- NAT gateways for internet access
- VPC endpoints for S3 and ECR
- Proper subnet tagging for EKS integration

### EKS Module (`eks/`)
Deploys an EKS cluster with:
- Managed node groups with auto-scaling
- Cluster addons (CoreDNS, VPC-CNI, EBS CSI driver)
- IRSA (IAM Roles for Service Accounts) support
- Karpenter integration for advanced auto-scaling

### IAM Module (`iam/`)
Manages IAM resources including:
- Service account roles with OIDC integration
- Policies for DynamoDB, SQS, and S3 access
- Cross-service permissions

### DynamoDB Module (`dynamodb/`)
Creates DynamoDB tables for:
- AI licenses management
- Conversation storage
- Message history
- User data
- Application settings

### SQS Module (`sqs/`)
Provides SQS queues for:
- AI core indexing processes
- Usage tracking
- Notifications
- Dead letter queues for error handling

### S3 Module (`s3/`)
Creates S3 buckets for:
- AI model storage
- File repository
- Backups
- Application logs

## 🚀 Usage

These modules are designed to be used with Terragrunt. Example usage:

```hcl
terraform {
  source = "git::https://dev.azure.com/n5/Platform/_git/fisnky-aws-terraform-modules.git//vpc?ref=v1.0.0"
}

inputs = {
  vpc_name = "fisnky-sandbox"
  cidr     = "10.0.0.0/16"
  # ... other variables
}
```

For local development:
```hcl
terraform {
  source = "../../../fisnky-aws-terraform-modules//vpc"
}
```

## 📋 Prerequisites

- **Terraform** >= 1.0
- **AWS Provider** ~> 5.0
- Appropriate AWS credentials configured

## 🔄 Versioning

This repository follows semantic versioning. When making changes:

1. Update the module code
2. Test changes thoroughly
3. Create a new tag: `git tag v1.1.0`
4. Push the tag: `git push origin v1.1.0`
5. Update module references in terragrunt configurations

## 🧪 Testing

Before releasing new versions:

1. Test modules individually with `terraform plan`
2. Test integration with terragrunt configurations
3. Validate in sandbox environment first

## 📚 Related Repositories

- **fisnky-aws-terragrunt**: Contains the Terragrunt configurations that use these modules
- **fisnky-applications**: Application deployments (ArgoCD, Helm charts)

## 🤝 Contributing

1. Create a feature branch from `main`
2. Make your changes
3. Test thoroughly in sandbox environment
4. Create a pull request
5. After approval and merge, tag a new version

## 📖 Module Documentation

Each module contains its own documentation in the respective directory. Key files:
- `README.md` - Module-specific documentation
- `variables.tf` - Input variables with descriptions
- `outputs.tf` - Output values with descriptions
- `main.tf` - Main module logic

## 🏷️ Tagging Strategy

- `v1.0.0` - Initial release
- `v1.1.0` - New features (backward compatible)
- `v1.0.1` - Bug fixes
- `v2.0.0` - Breaking changes

## 🔧 Development Workflow

### Clone project

```bash
git clone https://dev.azure.com/n5/Platform/_git/fisnky-aws-terraform-modules
cd fisnky-aws-terraform-modules
# To do plan/apply your code create a PR on Azure DevOps.
```

### Installation of pre-commit for Ubuntu 20.04 and higher

```bash
sudo apt update
sudo apt install -y unzip software-properties-common python3 python3-pip
python3 -m pip install --upgrade pip
pip install pre-commit terraform-docs
curl -L "$(curl -s https://api.github.com/repos/terraform-docs/terraform-docs/releases/latest | grep -o -E -m 1 "https://.+?-linux-amd64.tar.gz")" > terraform-docs.tgz && tar -xzf terraform-docs.tgz terraform-docs && rm terraform-docs.tgz && chmod +x terraform-docs && sudo mv terraform-docs /usr/bin/
cd fisnky-aws-terraform-modules/
pre-commit install
```

### Pipeline

A pipeline is included in this repository in the azure-pipelines.yml file.

Once a PR on main is approved, 3 stages are triggered:
#### terraform-lint:
- terraform_lint
- terraform fmt -check
- terraform validate
#### publish_tag:
- create the tag using versioning configured in the package.json file

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
