output "vpc_id" {
  description = "The `vpc_id` output provides the identifier of the Virtual Private Cloud (VPC)."
  value       = module.vpc.vpc_id
}

output "private_subnets" {
  description = "The `private_subnets` output provides the private subnets"
  value       = module.vpc.private_subnets
}

output "public_subnets" {
  description = "The `public_subnets` variable specifies the public subnets"
  value       = module.vpc.public_subnets
}

output "vpc_cidr_block" {
  description = "The `vpc_cidr_block` variable defines the CIDR block for the Virtual Private Cloud (VPC)"
  value       = module.vpc.vpc_cidr_block
}
