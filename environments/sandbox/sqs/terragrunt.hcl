include {
  path = find_in_parent_folders("terragrunt.hcl")
}

locals {
  common_vars = yamldecode(file("../common_vars.yaml"))
}

terraform {
  source = "git::https://dev.azure.com/n5/Platform/_git/fisnky-aws-terraform-modules.git//sqs?ref=v1.0.0"
  # For local development, use:
  # source = "../../../fisnky-aws-terraform-modules//sqs"
}

inputs = {
  # Environment
  environment = local.common_vars.env
  
  # Enable/Disable queues
  enable_indexing_queue      = true
  enable_usage_queue         = true
  enable_notifications_queue = true
  
  # Queue Configuration
  visibility_timeout_seconds = 300
  message_retention_seconds  = 1209600  # 14 days
  max_receive_count         = 3
  
  # Tags
  tags = local.common_vars.tags
}
