terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "5.75.1"
    }
  }
}

module "eks" {
  source         = "terraform-aws-modules/eks/aws"
  version        = "20.29.0"
  access_entries = var.access_entries
  tags           = var.tags

  cluster_name                    = var.cluster_name
  cluster_version                 = var.cluster_version
  cluster_addons                  = var.cluster_addons
  cluster_enabled_log_types       = var.cluster_enabled_log_types
  cluster_endpoint_public_access  = var.cluster_endpoint_public_access
  cluster_endpoint_private_access = var.cluster_endpoint_private_access

  enable_irsa                              = true
  enable_cluster_creator_admin_permissions = var.enable_cluster_creator_admin_permissions

  // Networking + SecurityGroups
  vpc_id                                       = var.vpc_id
  subnet_ids                                   = var.subnet_ids
  node_security_group_enable_recommended_rules = var.node_security_group_recommended_rules
  cluster_security_group_additional_rules      = merge(var.cluster_security_group_additional_rules, local.cluster_sg_platform)
  node_security_group_additional_rules         = merge(var.node_security_group_additional_rules, local.node_sg_platform)

  eks_managed_node_groups         = var.cluster_managed_node_groups_list
  eks_managed_node_group_defaults = var.cluster_managed_node_groups_defaults
  cloudwatch_log_group_retention_in_days = var.cloudwatch_log_group_retention_in_days 
}
