site_name: Terraform AWS EKS
docs_dir: docs/
site_url: https://terraform-aws-modules/terraform-aws-eks/
repo_name: terraform-aws-eks
repo_url: https://github.com/terraform-aws-modules/terraform-aws-eks

theme:
  name: material
  logo: assets/terraform-aws.png
  favicon: assets/logo.png
  font:
    text: ember
  palette:
    primary: deep purple
    accent: indgo
  icon:
    repo: fontawesome/brands/github
    admonition:
      note: octicons/tag-16
      abstract: octicons/checklist-16
      info: octicons/info-16
      tip: octicons/squirrel-16
      success: octicons/check-16
      question: octicons/question-16
      warning: octicons/alert-16
      failure: octicons/x-circle-16
      danger: octicons/zap-16
      bug: octicons/bug-16
      example: octicons/beaker-16
      quote: octicons/quote-16
  features:
    - navigation.tabs.sticky
  highlightjs: true
  hljs_languages:
    - yaml
    - json

plugins:
  - include-markdown
  - search:
      lang:
        - en
  - awesome-pages

extra:
  version:
    provider: mike

markdown_extensions:
  - attr_list
  - admonition
  - codehilite
  - footnotes
  - md_in_html
  - pymdownx.critic
  - pymdownx.details
  - pymdownx.highlight:
      anchor_linenums: true
      line_spans: __span
      pygments_lang_class: true
  - pymdownx.inlinehilite
  - pymdownx.snippets
  - pymdownx.superfences
  - toc:
      permalink: true
