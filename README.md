# Fisnky AWS Infrastructure with Terragrunt

This repository contains the simplified infrastructure configuration for Fisnky project using Terragrunt for deployment orchestration.

## 🏗️ Architecture

This setup deploys a complete AWS infrastructure including:

- **VPC** with public/private subnets across 3 AZs
- **EKS Cluster** with managed node groups
- **IAM Roles** for service accounts with proper permissions
- **DynamoDB Tables** for application data
- **SQS Queues** for async processing
- **S3 Buckets** for file storage

## 📁 Repository Structure

```
fisnky-aws-terragrunt/
├── environments/
│   └── sandbox/                    # Sandbox environment
│       ├── terragrunt.hcl         # Root configuration
│       ├── common_vars.yaml       # Environment variables
│       ├── vpc/terragrunt.hcl     # VPC configuration
│       ├── eks/terragrunt.hcl     # EKS configuration
│       ├── iam/terragrunt.hcl     # IAM configuration
│       ├── dynamodb/terragrunt.hcl # DynamoDB configuration
│       ├── sqs/terragrunt.hcl     # SQS configuration
│       └── s3/terragrunt.hcl      # S3 configuration
└── globals/
    └── versions.tf                # Terraform version constraints
```

## 🚀 Quick Start

### Prerequisites

1. **AWS CLI** configured with appropriate credentials
2. **Terraform** >= 1.0
3. **Terragrunt** >= 0.45
4. **Git** access to the modules repository

### Setup

1. **Clone this repository:**
   ```bash
   git clone https://dev.azure.com/n5/Platform/_git/fisnky-aws-terragrunt
   cd fisnky-aws-terragrunt
   ```

2. **Update configuration:**
   - Edit `environments/sandbox/common_vars.yaml`
   - Set your AWS account ID
   - Adjust VPC CIDR and subnets if needed
   - Update module source URLs

3. **Create S3 bucket for state:**
   ```bash
   aws s3 mb s3://fisnky-terraform-state
   aws dynamodb create-table \
     --table-name fisnky-terraform-state-lock \
     --attribute-definitions AttributeName=LockID,AttributeType=S \
     --key-schema AttributeName=LockID,KeyType=HASH \
     --billing-mode PAY_PER_REQUEST
   ```

### Deployment

Deploy infrastructure in order (due to dependencies):

```bash
cd environments/sandbox

# 1. Deploy VPC first
cd vpc
terragrunt apply

# 2. Deploy DynamoDB, SQS, S3 (can be parallel)
cd ../dynamodb && terragrunt apply
cd ../sqs && terragrunt apply  
cd ../s3 && terragrunt apply

# 3. Deploy EKS (depends on VPC)
cd ../eks
terragrunt apply

# 4. Deploy IAM (depends on all others)
cd ../iam
terragrunt apply
```

Or deploy everything at once:
```bash
cd environments/sandbox
terragrunt run-all apply
```

## 🔧 Configuration

### Environment Variables

Key variables in `common_vars.yaml`:

- **aws.aws_account_id**: Your AWS account ID
- **vpc.cidr**: VPC CIDR block
- **vpc.private_subnets**: Private subnet CIDRs
- **vpc.public_subnets**: Public subnet CIDRs
- **eks.cluster_version**: Kubernetes version
- **eks.node_groups**: Node group configuration

### Module Sources

Update module sources in each `terragrunt.hcl`:

```hcl
terraform {
  source = "git::https://dev.azure.com/n5/Platform/_git/fisnky-aws-terraform-modules.git//vpc?ref=v1.0.0"
}
```

For local development:
```hcl
terraform {
  source = "../../../fisnky-aws-terraform-modules//vpc"
}
```

## 🏷️ Outputs

After deployment, key outputs include:

- **VPC ID** and subnet IDs
- **EKS cluster** name and endpoint
- **DynamoDB table** names and ARNs
- **SQS queue** URLs and ARNs
- **S3 bucket** names and ARNs
- **IAM role** ARNs for service accounts

## 🔄 Adding New Environments

To add a new environment (e.g., `dev`):

1. Copy the `sandbox` folder:
   ```bash
   cp -r environments/sandbox environments/dev
   ```

2. Update `environments/dev/common_vars.yaml`:
   - Change `env: "dev"`
   - Update AWS account ID if different
   - Adjust VPC CIDR to avoid conflicts

3. Deploy the new environment:
   ```bash
   cd environments/dev
   terragrunt run-all apply
   ```

## 🧹 Cleanup

To destroy infrastructure:

```bash
cd environments/sandbox
terragrunt run-all destroy
```

## 📚 Related Repositories

- **fisnky-aws-terraform-modules**: Contains the reusable Terraform modules
- **fisnky-applications**: Application deployments (ArgoCD, Helm charts)

## 🤝 Contributing

1. Make changes to modules in `fisnky-aws-terraform-modules`
2. Tag new module versions
3. Update module references in this repository
4. Test in sandbox environment first
