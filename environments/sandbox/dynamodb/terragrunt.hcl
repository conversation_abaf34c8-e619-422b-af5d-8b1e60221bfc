include {
  path = find_in_parent_folders("terragrunt.hcl")
}

locals {
  common_vars = yamldecode(file("../common_vars.yaml"))
}

terraform {
  source = "git::https://dev.azure.com/n5/Platform/_git/fisnky-aws-terraform-modules.git//dynamodb?ref=v1.0.0"
  # For local development, use:
  # source = "../../../fisnky-aws-terraform-modules//dynamodb"
}

inputs = {
  # Environment
  environment = local.common_vars.env
  
  # Enable/Disable tables
  enable_ai_licenses   = true
  enable_conversations = true
  enable_messages      = true
  enable_users         = true
  enable_settings      = true
  
  # Tags
  tags = local.common_vars.tags
}
