include {
  path = find_in_parent_folders("terragrunt.hcl")
}

locals {
  common_vars = yamldecode(file("../common_vars.yaml"))
}

# Using inline Terraform configuration for S3 buckets
terraform {
  source = "."
}

# Create main.tf for S3 buckets
generate "main" {
  path      = "main.tf"
  if_exists = "overwrite"
  contents  = <<EOF
# S3 Buckets for Fisnky Application

resource "aws_s3_bucket" "ai_core_models" {
  bucket = "fisnky-\${var.environment}-ai-core-models"
  tags   = var.tags
}

resource "aws_s3_bucket_versioning" "ai_core_models" {
  bucket = aws_s3_bucket.ai_core_models.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "ai_core_models" {
  bucket = aws_s3_bucket.ai_core_models.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

resource "aws_s3_bucket" "file_repository" {
  bucket = "fisnky-\${var.environment}-file-repository"
  tags   = var.tags
}

resource "aws_s3_bucket_versioning" "file_repository" {
  bucket = aws_s3_bucket.file_repository.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "file_repository" {
  bucket = aws_s3_bucket.file_repository.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

resource "aws_s3_bucket" "backups" {
  bucket = "fisnky-\${var.environment}-backups"
  tags   = var.tags
}

resource "aws_s3_bucket_versioning" "backups" {
  bucket = aws_s3_bucket.backups.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "backups" {
  bucket = aws_s3_bucket.backups.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

resource "aws_s3_bucket" "logs" {
  bucket = "fisnky-\${var.environment}-logs"
  tags   = var.tags
}

resource "aws_s3_bucket_versioning" "logs" {
  bucket = aws_s3_bucket.logs.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "logs" {
  bucket = aws_s3_bucket.logs.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}
EOF
}

# Create variables.tf
generate "variables" {
  path      = "variables.tf"
  if_exists = "overwrite"
  contents  = <<EOF
variable "environment" {
  description = "Environment name"
  type        = string
}

variable "tags" {
  description = "Tags to apply to resources"
  type        = map(string)
  default     = {}
}
EOF
}

# Create outputs.tf
generate "outputs" {
  path      = "outputs.tf"
  if_exists = "overwrite"
  contents  = <<EOF
output "ai_core_models_bucket_name" {
  description = "Name of the AI core models bucket"
  value       = aws_s3_bucket.ai_core_models.bucket
}

output "ai_core_models_bucket_arn" {
  description = "ARN of the AI core models bucket"
  value       = aws_s3_bucket.ai_core_models.arn
}

output "file_repository_bucket_name" {
  description = "Name of the file repository bucket"
  value       = aws_s3_bucket.file_repository.bucket
}

output "file_repository_bucket_arn" {
  description = "ARN of the file repository bucket"
  value       = aws_s3_bucket.file_repository.arn
}

output "backups_bucket_name" {
  description = "Name of the backups bucket"
  value       = aws_s3_bucket.backups.bucket
}

output "backups_bucket_arn" {
  description = "ARN of the backups bucket"
  value       = aws_s3_bucket.backups.arn
}

output "logs_bucket_name" {
  description = "Name of the logs bucket"
  value       = aws_s3_bucket.logs.bucket
}

output "logs_bucket_arn" {
  description = "ARN of the logs bucket"
  value       = aws_s3_bucket.logs.arn
}
EOF
}

inputs = {
  environment = local.common_vars.env
  tags        = local.common_vars.tags
}
