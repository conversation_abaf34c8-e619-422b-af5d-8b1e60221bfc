variable "region" {
  type        = string
  default     = "us-east-1"
  description = "The `region` variable specifies the AWS region"
}
variable "cidr_blocks" {
  type        = list(string)
  default     = [""]
  description = "The `cidr` variable defines the CIDR (Classless Inter-Domain Routing) IP address range for a network"
}
variable "cluster_name" {
  type        = string
  default     = ""
  description = "The `cluster_name` variable specifies the name of the Kubernetes cluster"
}
variable "cluster_version" {
  type        = string
  default     = "1.28"
  description = "The `cluster_version` variable specifies the version of the Kubernetes cluster."
}
variable "cluster_endpoint_public_access" {
  type        = bool
  default     = true
  description = "The `cluster_endpoint_public_access` variable determines whether the cluster's endpoint is publicly accessible"
}
variable "cluster_addons" {
  type = any
  default = {
    coredns = {
      most_recent = true
    }
    kube-proxy = {
      most_recent = true
    }
    vpc-cni = {
      most_recent = true
    }
    amazon-cloudwatch-observability = {
      most_recent = true
    }
  }
  description = "The `cluster_addons` variable specifies the addons to be enabled for the cluster"
}
variable "vpc_id" {
  type        = string
  default     = ""
  description = "The `vpc_id` variable represents the identifier of the Virtual Private Cloud (VPC)"
}
variable "subnet_ids" {
  type        = list(string)
  default     = []
  description = "The `subnet_ids` variable specifies the IDs of the private subnets"
}
variable "instance_types" {
  type        = list(string)
  default     = ["r5.xlarge", "r5.large", "m5.xlarge", "m5.large"]
  description = "The `instance_types` variable specifies the types of instances to be used"
}

variable "enable_cluster_creator_admin_permissions" {
  type        = bool
  default     = true
  description = "The `enable_cluster_creator_admin_permissions` variable determines whether to enable administrative permissions for the cluster creator"
}
variable "cluster_endpoint_private_access" {
  type        = bool
  default     = false
  description = "The `cluster_endpoint_private_access` variable indicates whether the cluster endpoint has private access"
}
variable "cluster_enabled_log_types" {
  type        = list(string)
  default     = ["audit", "api", "authenticator", "scheduler"]
  description = "The `cluster_enabled_log_types` variable provides the enabled log types for the cluster"
}
variable "access_entries" {
  type        = any
  default     = {}
  description = "The `access_entries` variable provides information about access entries in a security group or network ACL"
}
variable "tags" {
  description = "The `tags` variable specifies the tags to be applied to resources"
  type        = map(string)
  default = {
    "Owner"     = "Devops"
    "Terraform" = "true"
  }
}

variable "karpenter_enabled" {
  type        = bool
  default     = true
  description = "The `karpenter_enabled` variable determines whether Karpenter, the Kubernetes cluster autoscaler, is enabled"
}

variable "karpenter_enable_pod_identity" {
  type        = bool
  default     = true
  description = "value"
}

variable "karpenter_create_pod_identity_association" {
  type        = bool
  default     = false
  description = "The `karpenter_enable_pod_identity` variable determines whether to enable pod identity for Karpenter"
}

variable "create_node_iam_role" {
  type        = bool
  default     = false
  description = "The `create_node_iam_role` variable determines whether to create an IAM role for the nodes"
}

variable "create_access_entry" {
  type        = bool
  default     = false
  description = "The `create_access_entry` variable determines whether to create an access entry"
}

variable "create_instance_profile" {
  type        = bool
  default     = false
  description = "The `create_instance_profile` variable determines whether to create an instance profile"
}

variable "node_security_group_additional_rules" {
  type        = map(any)
  default     = {}
  description = "additional node securitygroup rules"
}

variable "node_security_group_recommended_rules" {
  type        = bool
  default     = true
  description = "additional node securitygroup rules"
}

variable "cluster_security_group_additional_rules" {
  type        = map(any)
  default     = {}
  description = "additional cluster security group rules"
}


variable "cluster_managed_node_groups_list" {
  description = "define managed node groups"
  type        = any
  default     = {}
}

variable "cluster_managed_node_groups_defaults" {
  description = "define managed node groups default parameters"
  type        = any
  default = {
    iam_role_additional_policies = { AmazonSSMManagedInstanceCore = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore" }
    instance_types               = ["r5.xlarge", "r5.large", "m5.xlarge", "m5.large"]
    capacity_type                = "SPOT"
    min_size                     = 1
    max_size                     = 6
  }
}

variable "cloudwatch_log_group_retention_in_days" {
  description = "retention time in days"
  default = 30
  type = number 
}
