terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

# SQS Queue for AI Core Indexing
resource "aws_sqs_queue" "indexing_queue" {
  name                      = "${var.environment}-ai-core-indexing"
  delay_seconds             = 0
  max_message_size          = 262144
  message_retention_seconds = 1209600  # 14 days
  receive_wait_time_seconds = 0
  visibility_timeout_seconds = 300

  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.indexing_dlq.arn
    maxReceiveCount     = 3
  })

  tags = merge(var.tags, {
    Name = "${var.environment}-ai-core-indexing"
    Service = "ai-core-indexer"
  })
}

# Dead Letter Queue for Indexing
resource "aws_sqs_queue" "indexing_dlq" {
  name                      = "${var.environment}-ai-core-indexing-dlq"
  message_retention_seconds = 1209600  # 14 days

  tags = merge(var.tags, {
    Name = "${var.environment}-ai-core-indexing-dlq"
    Service = "ai-core-indexer"
  })
}

# SQS Queue for Usage Tracking
resource "aws_sqs_queue" "usage_queue" {
  name                      = "${var.environment}-ai-core-usage"
  delay_seconds             = 0
  max_message_size          = 262144
  message_retention_seconds = 1209600  # 14 days
  receive_wait_time_seconds = 0
  visibility_timeout_seconds = 300

  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.usage_dlq.arn
    maxReceiveCount     = 3
  })

  tags = merge(var.tags, {
    Name = "${var.environment}-ai-core-usage"
    Service = "ai-core-api"
  })
}

# Dead Letter Queue for Usage
resource "aws_sqs_queue" "usage_dlq" {
  name                      = "${var.environment}-ai-core-usage-dlq"
  message_retention_seconds = 1209600  # 14 days

  tags = merge(var.tags, {
    Name = "${var.environment}-ai-core-usage-dlq"
    Service = "ai-core-api"
  })
}

# SQS Queue for Notifications
resource "aws_sqs_queue" "notifications_queue" {
  name                      = "${var.environment}-notifications"
  delay_seconds             = 0
  max_message_size          = 262144
  message_retention_seconds = 1209600  # 14 days
  receive_wait_time_seconds = 0
  visibility_timeout_seconds = 300

  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.notifications_dlq.arn
    maxReceiveCount     = 3
  })

  tags = merge(var.tags, {
    Name = "${var.environment}-notifications"
    Service = "notifications"
  })
}

# Dead Letter Queue for Notifications
resource "aws_sqs_queue" "notifications_dlq" {
  name                      = "${var.environment}-notifications-dlq"
  message_retention_seconds = 1209600  # 14 days

  tags = merge(var.tags, {
    Name = "${var.environment}-notifications-dlq"
    Service = "notifications"
  })
}
