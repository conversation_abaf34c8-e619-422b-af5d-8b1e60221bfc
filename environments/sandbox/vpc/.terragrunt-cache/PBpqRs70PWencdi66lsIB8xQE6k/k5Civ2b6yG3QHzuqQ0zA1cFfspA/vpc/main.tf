## AWS Provider
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "5.37.0" # Cambiar por la versión necesaria.
    }
  }
}

module "vpc" {
  source  = "terraform-aws-modules/vpc/aws"
  version = "5.5.2"

  name = "${var.vpc_name}-vpc"
  cidr = var.cidr

  azs             = var.azs
  private_subnets = var.private_subnets
  public_subnets  = var.public_subnets

  enable_nat_gateway   = var.enable_nat_gateway
  single_nat_gateway   = var.single_nat_gateway
  enable_dns_hostnames = var.enable_dns_hostnames
  enable_dns_support   = var.enable_dns_support


  one_nat_gateway_per_az               = var.one_nat_gateway_per_az
  enable_vpn_gateway                   = var.enable_vpn_gateway
  enable_network_address_usage_metrics = var.enable_network_address_usage_metrics

  tags = var.tags

  public_subnet_tags = var.public_subnet_tags

  private_subnet_tags = var.private_subnet_tags

  nat_gateway_tags = var.nat_gateway_tags
  #VPC FLow logs
  create_flow_log_cloudwatch_iam_role             = true
  create_flow_log_cloudwatch_log_group            = true
  enable_flow_log                                 = true
  flow_log_cloudwatch_log_group_retention_in_days = 7
  flow_log_cloudwatch_log_group_name_prefix       = "/aws/vpc-flow-log/${var.vpc_name}"

}

resource "aws_vpc_endpoint" "s3" {
  depends_on        = [module.vpc]
  vpc_id            = module.vpc.vpc_id
  service_name      = var.service_name_s3
  vpc_endpoint_type = var.vpc_endpoint_type_s3
  tags              = var.tags
}

resource "aws_vpc_endpoint" "ecr" {
  depends_on        = [module.vpc]
  vpc_id            = module.vpc.vpc_id
  service_name      = var.service_name_ecr
  vpc_endpoint_type = var.vpc_endpoint_type_ecr
  tags              = var.tags
}

